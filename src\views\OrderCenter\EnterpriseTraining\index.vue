<!--
  页面名称：企业培训订单首页
  功能描述：展示企业培训订单列表，支持搜索、分页、新增、编辑、删除、查看操作日志
-->
<template>
  <div class="enterprise-training-index">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statsData.totalOrders }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statsData.pendingOrders }}</div>
            <div class="stat-label">待处理订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">¥{{ statsData.monthlyAmount.toLocaleString() }}</div>
            <div class="stat-label">本月订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statsData.completionRate }}%</div>
            <div class="stat-label">订单完成率</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <div class="search-form">
        <el-form :inline="true" :model="searchForm" @submit.prevent="onSearch">
          <el-form-item label="订单状态">
            <el-select
              v-model="searchForm.orderStatus"
              placeholder="全部订单状态"
              clearable
              style="width: 180px"
            >
              <el-option label="全部订单状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="待审批" value="pending_approval" />
              <el-option label="审批中" value="approving" />
              <el-option label="待支付" value="pending_payment" />
              <el-option label="待履约" value="pending_fulfillment" />
              <el-option label="履约中" value="fulfilling" />
              <el-option label="已完成" value="completed" />
              <el-option label="已关闭" value="closed" />
              <el-option label="审批驳回" value="approval_rejected" />
            </el-select>
          </el-form-item>
          <el-form-item label="支付状态">
            <el-select
              v-model="searchForm.paymentStatus"
              placeholder="全部支付状态"
              clearable
              style="width: 180px"
            >
              <el-option label="全部支付状态" value="" />
              <el-option label="未支付" value="unpaid" />
              <el-option label="已支付" value="paid" />
              <el-option label="已退款" value="refunded" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索企业名称、培训项目..."
              style="width: 300px"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">搜索</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="action-buttons">
        <el-button type="success" @click="onAdd">
          <el-icon><Plus /></el-icon>
          + 新建企业培训订单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        :header-cell-style="{ background: '#fafafa', color: '#606266', fontWeight: '600' }"
        :row-style="{ cursor: 'pointer' }"
        @row-click="onRowClick"
      >
        <el-table-column prop="orderNumber" label="订单号" width="150" />
        <el-table-column prop="companyName" label="企业名称" width="200" />
        <el-table-column prop="trainingProject" label="培训项目" width="250" />
        <el-table-column prop="traineeCount" label="培训人数" width="100" />
        <el-table-column prop="trainingPeriod" label="培训周期" width="200" />
        <el-table-column prop="orderAmount" label="订单金额" width="120">
          <template #default="scope"> ¥{{ scope.row.orderAmount }} </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click.stop="onView(scope.row)">查看</el-button>
            <el-button size="small" type="warning" @click.stop="onEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click.stop="onDelete(scope.row)">删除</el-button>
            <el-button size="small" @click.stop="onOptLog(scope.row)">操作日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <AddEnterpriseTraining
      v-model:visible="drawerVisible"
      :edit-data="editData"
      @success="onSuccess"
    />

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-id="currentOrderId" />

    <!-- 查看详情抽屉 -->
    <EnterpriseTrainingView
      v-model:visible="viewVisible"
      :order-data="viewData"
      @edit="onEditFromView"
      @view-full-log="onViewFullLogFromView"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import AddEnterpriseTraining from './components/AddEnterpriseTraining.vue'
import OptLog from './components/OptLog.vue'
import EnterpriseTrainingView from './components/EnterpriseTrainingView.vue'
import type { EnterpriseTrainingOrder } from '@/api/OrderCenter/enterprise-training'

// 响应式数据
/** 搜索表单数据 */
const searchForm = ref({
  orderStatus: '',
  paymentStatus: '',
  keyword: ''
})

/** 表格数据 */
const tableData = ref<EnterpriseTrainingOrder[]>([
  {
    id: 1,
    orderNumber: 'ET202406001',
    companyName: 'ABC科技有限公司',
    trainingProject: '数字化转型管理培训',
    traineeCount: 25,
    trainingPeriod: '2024.07.01 - 2024.07.15',
    orderAmount: 125000,
    orderStatus: 'fulfilling',
    paymentStatus: 'paid',
    createTime: '2024-06-15'
  },
  {
    id: 2,
    orderNumber: 'ET202406002',
    companyName: 'XYZ制造集团',
    trainingProject: '精益生产管理培训',
    traineeCount: 40,
    trainingPeriod: '2024.07.20 - 2024.08.10',
    orderAmount: 180000,
    orderStatus: 'pending_fulfillment',
    paymentStatus: 'paid',
    createTime: '2024-06-18'
  },
  {
    id: 3,
    orderNumber: 'ET202406003',
    companyName: '创新科技集团',
    trainingProject: '团队协作与沟通技巧培训',
    traineeCount: 60,
    trainingPeriod: '2024.08.01 - 2024.08.05',
    orderAmount: 240000,
    orderStatus: 'pending_approval',
    paymentStatus: 'unpaid',
    createTime: '2024-06-20'
  },
  {
    id: 4,
    orderNumber: 'ET202406004',
    companyName: '星火传媒',
    trainingProject: '新媒体运营进阶',
    traineeCount: 20,
    trainingPeriod: '2024.07.10 - 2024.07.12',
    orderAmount: 80000,
    orderStatus: 'approval_rejected',
    paymentStatus: 'cancelled',
    createTime: '2024-06-21'
  }
])

/** 统计数据 */
const statsData = ref({
  totalOrders: 18,
  pendingOrders: 0,
  monthlyAmount: 2135220,
  completionRate: 92.5
})

/** 分页信息 */
const pagination = ref({
  page: 1,
  size: 10,
  total: 4
})

/** 加载状态 */
const loading = ref(false)

/** 抽屉显示状态 */
const drawerVisible = ref(false)
const optLogVisible = ref(false)
const viewVisible = ref(false)

/** 编辑数据 */
const editData = ref<EnterpriseTrainingOrder | null>(null)

/** 查看数据 */
const viewData = ref<EnterpriseTrainingOrder | null>(null)

/** 当前订单ID */
const currentOrderId = ref('')

// 方法
/** 获取订单状态类型 */
const getOrderStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'primary',
    pending_payment: 'warning',
    pending_fulfillment: 'info',
    fulfilling: 'primary',
    completed: 'success',
    closed: 'info',
    approval_rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    pending_payment: '待支付',
    pending_fulfillment: '待履约',
    fulfilling: '履约中',
    completed: '已完成',
    closed: '已关闭',
    approval_rejected: '审批驳回'
  }
  return statusMap[status] || status
}

/** 获取支付状态类型 */
const getPaymentStatusType = (
  status: string
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    unpaid: 'warning',
    paid: 'success',
    refunded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unpaid: '未支付',
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

/** 重置搜索 */
const onReset = () => {
  searchForm.value = {
    orderStatus: '',
    paymentStatus: '',
    keyword: ''
  }
  onSearch()
}

/** 获取列表数据 */
const fetchList = async () => {
  loading.value = true
  try {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 使用静态数据源
    const staticData: EnterpriseTrainingOrder[] = [
      {
        id: 1,
        orderNumber: 'ET202406001',
        companyName: 'ABC科技有限公司',
        trainingProject: '数字化转型管理培训',
        traineeCount: 25,
        trainingPeriod: '2024.07.01 - 2024.07.15',
        orderAmount: 125000,
        orderStatus: 'fulfilling',
        paymentStatus: 'paid',
        createTime: '2024-06-15'
      },
      {
        id: 2,
        orderNumber: 'ET202406002',
        companyName: 'XYZ制造集团',
        trainingProject: '精益生产管理培训',
        traineeCount: 40,
        trainingPeriod: '2024.07.20 - 2024.08.10',
        orderAmount: 180000,
        orderStatus: 'pending_fulfillment',
        paymentStatus: 'paid',
        createTime: '2024-06-18'
      },
      {
        id: 3,
        orderNumber: 'ET202406003',
        companyName: '创新科技集团',
        trainingProject: '团队协作与沟通技巧培训',
        traineeCount: 60,
        trainingPeriod: '2024.08.01 - 2024.08.05',
        orderAmount: 240000,
        orderStatus: 'pending_approval',
        paymentStatus: 'unpaid',
        createTime: '2024-06-20'
      },
      {
        id: 4,
        orderNumber: 'ET202406004',
        companyName: '星火传媒',
        trainingProject: '新媒体运营进阶',
        traineeCount: 20,
        trainingPeriod: '2024.07.10 - 2024.07.12',
        orderAmount: 80000,
        orderStatus: 'approval_rejected',
        paymentStatus: 'cancelled',
        createTime: '2024-06-21'
      }
    ]

    // 过滤数据
    const filteredData = staticData.filter((item) => {
      if (searchForm.value.orderStatus && item.orderStatus !== searchForm.value.orderStatus)
        return false
      if (searchForm.value.paymentStatus && item.paymentStatus !== searchForm.value.paymentStatus)
        return false
      if (searchForm.value.keyword) {
        const keyword = searchForm.value.keyword.toLowerCase()
        return (
          item.companyName.toLowerCase().includes(keyword) ||
          item.trainingProject.toLowerCase().includes(keyword)
        )
      }
      return true
    })

    // 模拟分页
    const start = (pagination.value.page - 1) * pagination.value.size
    const end = start + pagination.value.size
    const pagedData = filteredData.slice(start, end)

    tableData.value = pagedData
    pagination.value.total = filteredData.length
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

/** 获取统计数据 */
const fetchStats = async () => {
  try {
    // 使用静态统计数据
    statsData.value = {
      totalOrders: 18,
      pendingOrders: 0,
      monthlyAmount: 2135220,
      completionRate: 92.5
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

/** 新增 */
const onAdd = () => {
  editData.value = null
  drawerVisible.value = true
}

/** 编辑 */
const onEdit = (row: EnterpriseTrainingOrder) => {
  editData.value = { ...row }
  drawerVisible.value = true
}

/** 查看 */
const onView = (row: EnterpriseTrainingOrder) => {
  viewData.value = { ...row }
  viewVisible.value = true
}

/** 从查看页面编辑 */
const onEditFromView = (row: EnterpriseTrainingOrder) => {
  viewVisible.value = false
  editData.value = { ...row }
  drawerVisible.value = true
}

/** 从查看页面查看完整日志 */
const onViewFullLogFromView = (row: EnterpriseTrainingOrder | null) => {
  if (row) {
    currentOrderId.value = row.orderNumber || ''
    optLogVisible.value = true
  }
}

/** 删除 */
const onDelete = async (row: EnterpriseTrainingOrder) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 模拟删除操作
    const index = tableData.value.findIndex((item) => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')

      // 如果当前页没有数据了，且不是第一页，则跳转到上一页
      if (tableData.value.length === 0 && pagination.value.page > 1) {
        pagination.value.page--
      }

      fetchList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

/** 操作日志 */
const onOptLog = (row: EnterpriseTrainingOrder) => {
  currentOrderId.value = row.orderNumber || ''
  optLogVisible.value = true
}

/** 行点击事件 */
const onRowClick = (row: EnterpriseTrainingOrder) => {
  onView(row)
}

/** 操作成功回调 */
const onSuccess = () => {
  drawerVisible.value = false
  fetchList()
}

/** 分页大小改变 */
const onSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchList()
}

/** 当前页改变 */
const onCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchList()
}

// 生命周期
onMounted(() => {
  fetchList()
  fetchStats()
})
</script>

<style scoped lang="scss">
.enterprise-training-index {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20px;

  .statistics-section {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border-left: 4px solid #409eff;
      text-align: center;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #409eff, #67c23a);
      }

      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .stat-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .search-section {
    background: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-form {
      flex: 1;
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      margin-left: 20px;
    }
  }

  .table-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .pagination-section {
      padding: 24px;
      text-align: right;
      border-top: 1px solid #ebeef5;
      background: #fafafa;
    }
  }
}
</style>
