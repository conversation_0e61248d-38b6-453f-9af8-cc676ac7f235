<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="企业培训订单详情"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="view-container">
      <!-- 培训项目头部 -->
      <div class="project-header">
        <div class="project-name">{{ orderData.trainingProject }}</div>
        <el-tag type="primary" class="status-tag">{{
          getOrderStatusText(orderData.orderStatus)
        }}</el-tag>
      </div>

      <!-- 订单基本信息 -->
      <div class="info-section">
        <div class="info-item">
          <span class="label">订单号:</span>
          <span class="value">{{ orderData.orderNumber }}</span>
        </div>
        <div class="info-item">
          <span class="label">合作企业:</span>
          <span class="value">{{ orderData.companyName }}</span>
        </div>
        <div class="info-item">
          <span class="label">培训周期:</span>
          <span class="value">{{ orderData.trainingPeriod }}</span>
        </div>
        <div class="info-item">
          <span class="label">培训人数:</span>
          <span class="value">{{ orderData.traineeCount }}人</span>
        </div>
        <div class="info-item">
          <span class="label">订单金额:</span>
          <span class="value amount">¥{{ orderData.orderAmount?.toLocaleString() }}</span>
        </div>
        <div class="info-item">
          <span class="label">支付状态:</span>
          <el-tag :type="getPaymentStatusType(orderData.paymentStatus)" class="status-tag">
            {{ getPaymentStatusText(orderData.paymentStatus) }}
          </el-tag>
        </div>
        <div class="info-item">
          <span class="label">我方负责人:</span>
          <span class="value">{{ orderData.manager }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间:</span>
          <span class="value">{{ orderData.createTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联商机:</span>
          <span class="value">{{ orderData.businessOpportunity || '无' }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联线索:</span>
          <span class="value">{{ orderData.lead || '无' }}</span>
        </div>
      </div>

      <!-- 合同管理 -->
      <div class="section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <span class="section-title">合同管理</span>
        </div>
        <div class="contract-tabs">
          <el-tabs v-model="activeContractTab">
            <el-tab-pane label="电子合同" name="electronic">
              <div class="contract-status">
                <div class="status-item">
                  <div class="status-info">
                    <el-icon class="status-icon"><Document /></el-icon>
                    <span class="status-label">平台方</span>
                  </div>
                  <el-tag type="success" size="small">已签署</el-tag>
                </div>
                <div class="status-item">
                  <div class="status-info">
                    <el-icon class="status-icon"><OfficeBuilding /></el-icon>
                    <span class="status-label">企业方</span>
                  </div>
                  <el-tag type="warning" size="small">待签署</el-tag>
                </div>
                <div class="contract-actions">
                  <el-button size="small">查看协议</el-button>
                  <el-button type="primary" size="small">发起签约</el-button>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="纸质合同" name="paper">
              <div class="paper-contract">
                <div class="contract-file">
                  <div class="file-label">纸质合同附件</div>
                  <div class="file-item">
                    <div class="file-info">
                      <el-icon class="file-icon"><Document /></el-icon>
                      <span class="file-name">ABC科技培训合同.pdf</span>
                    </div>
                    <el-button size="small" type="primary" @click="onDownloadContract"
                      >下载</el-button
                    >
                  </div>
                  <el-button
                    type="primary"
                    size="small"
                    class="upload-btn"
                    @click="onUploadContract"
                  >
                    <el-icon><Upload /></el-icon>
                    上传纸质合同
                  </el-button>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 收款信息 -->
      <div class="section">
        <div class="section-header">
          <el-icon class="section-icon"><Money /></el-icon>
          <span class="section-title">收款信息</span>
        </div>
        <div class="receipt-info">
          <div class="receipt-item">
            <span class="label">收款金额:</span>
            <span class="value amount">¥{{ orderData.orderAmount?.toLocaleString() }}</span>
          </div>
          <div class="receipt-item">
            <span class="label">收款方式:</span>
            <span class="value">{{ getPaymentMethodText(orderData.paymentStatus) }}</span>
          </div>
          <div class="receipt-item">
            <span class="label">收款日期:</span>
            <span class="value">{{ getReceiptDate() }}</span>
          </div>
          <div class="receipt-item">
            <span class="label">操作人:</span>
            <span class="value">{{ orderData.manager || '系统' }}</span>
          </div>
          <div class="receipt-item">
            <span class="label">收款备注:</span>
            <span class="value">{{ getReceiptRemark() }}</span>
          </div>
        </div>
      </div>

      <!-- 审批流程 -->
      <div class="section">
        <div class="section-header">
          <el-icon class="section-icon"><Tools /></el-icon>
          <span class="section-title">审批流程</span>
        </div>
        <div class="approval-timeline">
          <el-timeline>
            <el-timeline-item timestamp="2024/6/14 11:00:00" placement="top" color="#409EFF">
              <div class="timeline-content">
                <div class="timeline-text">张三(管理员)批准了订单。</div>
                <div class="timeline-note">备注:审批通过</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="section">
        <div class="section-header">
          <el-icon class="section-icon"><Refresh /></el-icon>
          <span class="section-title">操作日志</span>
          <el-button size="small" class="view-log-btn" @click="onViewFullLog">
            <el-icon><List /></el-icon>
            查看完整日志
          </el-button>
        </div>
        <div class="operation-timeline">
          <el-timeline>
            <el-timeline-item timestamp="2024/6/14 10:30:00" placement="top" color="#409EFF">
              <div class="timeline-content">
                <div class="timeline-text">李四(管理员)创建企业培训订单</div>
              </div>
            </el-timeline-item>
            <el-timeline-item timestamp="2024/6/14 11:00:00" placement="top" color="#409EFF">
              <div class="timeline-content">
                <div class="timeline-text">张三(管理员)审批通过</div>
              </div>
            </el-timeline-item>
            <el-timeline-item timestamp="2024/6/14 16:45:00" placement="top" color="#409EFF">
              <div class="timeline-content">
                <div class="timeline-text">李四(管理员)确认收款完成</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="onEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Document,
  OfficeBuilding,
  Money,
  Tools,
  Refresh,
  List,
  Upload
} from '@element-plus/icons-vue'
import type { EnterpriseTrainingOrder } from '@/api/OrderCenter/enterprise-training'

// Props
interface Props {
  visible: boolean
  orderData?: EnterpriseTrainingOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [orderData: EnterpriseTrainingOrder]
  viewFullLog: [orderData: EnterpriseTrainingOrder | null]
}>()

// 响应式数据
const activeContractTab = ref('electronic')

// 计算属性
const orderData = computed(() => {
  return (
    props.orderData || {
      orderNumber: 'ET202406001',
      companyName: 'ABC科技有限公司',
      trainingProject: '数字化转型管理培训',
      trainingPeriod: '2024.07.01 - 2024.07.15',
      traineeCount: 25,
      orderAmount: 125000,
      orderStatus: 'fulfilling',
      paymentStatus: 'paid',
      manager: '李四',
      createTime: '2024-06-15',
      businessOpportunity: 'OPP202406005',
      lead: 'LEAD202406005'
    }
  )
})

// 方法
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    pending_payment: '待支付',
    pending_fulfillment: '待履约',
    fulfilling: '履约中',
    completed: '已完成',
    closed: '已关闭',
    approval_rejected: '审批驳回'
  }
  return statusMap[status] || status
}

const getPaymentStatusType = (status: string): 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'danger'> = {
    unpaid: 'warning',
    paid: 'success',
    refunded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unpaid: '未支付',
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getPaymentMethodText = (status: string) => {
  if (status === 'paid') {
    return 'bank_transfer'
  }
  return '未支付'
}

const getReceiptDate = () => {
  return '2024-06-14'
}

const getReceiptRemark = () => {
  return '银行转账'
}

const onViewFullLog = () => {
  // 触发查看完整日志事件
  emit('viewFullLog', props.orderData)
}

const onDownloadContract = () => {
  console.log('下载合同文件')
  // 这里可以添加下载逻辑
}

const onUploadContract = () => {
  console.log('上传纸质合同')
  // 这里可以添加上传逻辑
}

const onEdit = () => {
  if (props.orderData) {
    emit('edit', props.orderData)
  }
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.view-container {
  padding: 24px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.project-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .project-name {
    font-size: 20px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .status-tag {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
  }
}

.info-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 0;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: 600;
      color: #606266;
      min-width: 120px;
      margin-right: 16px;
    }

    .value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 18px;
      }
    }

    .status-tag {
      margin-left: 8px;
    }
  }
}

.section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #409eff;

    .section-icon {
      color: #409eff;
      margin-right: 12px;
      font-size: 18px;
    }

    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      flex: 1;
    }

    .view-log-btn {
      font-size: 12px;
    }
  }
}

.contract-status {
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 12px;
    background: white;
    border-radius: 6px;

    .status-info {
      display: flex;
      align-items: center;

      .status-icon {
        margin-right: 8px;
        color: #409eff;
      }

      .status-label {
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .contract-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
  }
}

.receipt-info {
  background: #f0f9ff;
  border: 1px solid #67c23a;
  border-radius: 6px;
  padding: 16px;

  .receipt-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      margin-right: 12px;
    }

    .value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 18px;
      }
    }
  }
}

.approval-timeline,
.operation-timeline {
  .timeline-content {
    background: white;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;

    .timeline-text {
      font-size: 14px;
      color: #303133;
      margin-bottom: 4px;
    }

    .timeline-note {
      font-size: 12px;
      color: #909399;
      background: #fff3cd;
      padding: 4px 8px;
      border-radius: 4px;
    }
  }
}

.paper-contract {
  .contract-file {
    .file-label {
      font-weight: 500;
      color: #606266;
      margin-bottom: 12px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 16px;

      .file-info {
        display: flex;
        align-items: center;

        .file-icon {
          color: #409eff;
          margin-right: 8px;
          font-size: 16px;
        }

        .file-name {
          color: #303133;
          font-size: 14px;
        }
      }
    }

    .upload-btn {
      width: 100%;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-timeline-item__node) {
  background-color: #409eff;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-timeline-item__content) {
  margin-left: 16px;
}

:deep(.el-tabs__item) {
  font-size: 14px;
}

:deep(.el-tabs__content) {
  padding-top: 16px;
}
</style>
