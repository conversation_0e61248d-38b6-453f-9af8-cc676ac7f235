<template>
  <el-drawer
    :model-value="visible"
    :title="`${orderData?.orderNumber || ''} - 操作日志`"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
    @update:model-value="handleUpdateVisible"
  >
    <div class="optlog-container">
      <el-timeline>
        <el-timeline-item
          v-for="(log, index) in logList"
          :key="index"
          :timestamp="log.time"
          placement="top"
          :color="getLogColor(log.action)"
        >
          <div class="log-item">
            <div class="log-header">
              <span class="log-user">{{ log.user }}</span>
              <el-button :type="getButtonType(log.action) as any" size="small" class="log-button">
                {{ getButtonText(log.action) }}
              </el-button>
            </div>

            <div class="log-content">
              <div class="log-action">{{ log.action }}</div>

              <!-- 变更详情 -->
              <div v-if="log.changes && log.changes.length > 0" class="log-changes">
                <div v-for="change in log.changes" :key="change.field" class="change-item">
                  <span class="change-field">{{ change.field }}:</span>
                  <span class="change-old">{{ change.old }}</span>
                  <span class="change-arrow">→</span>
                  <span class="change-new">{{ change.new }}</span>
                </div>
              </div>

              <!-- 备注 -->
              <div v-if="log.note" class="log-note">
                {{ log.note }}
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 操作日志数据
const logList = ref([
  {
    time: '2024/6/15 14:30:00',
    user: '张三 (管理员)',
    action: '创建高校实践订单',
    changes: [],
    note: '',
    type: 'create'
  },
  {
    time: '2024/6/16 09:15:00',
    user: '张三 (管理员)',
    action: '编辑高校实践订单',
    changes: [
      { field: '项目名称', old: '暑期社会实践项目', new: '→2024年暑期社会实践项目' },
      { field: '订单金额', old: '550000', new: '→580000' }
    ],
    note: '更新项目名称和金额',
    type: 'edit'
  },
  {
    time: '2024/6/20 10:05:12',
    user: '张三 (管理员)',
    action: '审批通过',
    changes: [{ field: '订单状态', old: '待审批', new: '→待支付' }],
    note: '已确认合作意向,批准立项',
    type: 'approve'
  },
  {
    time: '2024/6/20 15:30:00',
    user: '张三 (管理员)',
    action: '确认收款',
    changes: [
      { field: '支付状态', old: '待支付', new: '→已支付' },
      { field: '订单状态', old: '待支付', new: '→执行中' }
    ],
    note: '确认收款完成',
    type: 'payment'
  }
])

// 获取日志颜色
const getLogColor = (action: string) => {
  const colorMap: Record<string, string> = {
    创建高校实践订单: '#67C23A',
    编辑高校实践订单: '#E6A23C',
    审批通过: '#409EFF',
    确认收款: '#67C23A'
  }
  return colorMap[action] || '#909399'
}

// 获取按钮类型
const getButtonType = (action: string) => {
  const typeMap: Record<string, string> = {
    创建高校实践订单: 'success',
    编辑高校实践订单: 'warning',
    审批通过: 'primary',
    确认收款: 'success'
  }
  return typeMap[action] || 'info'
}

// 获取按钮文本
const getButtonText = (action: string) => {
  const textMap: Record<string, string> = {
    创建高校实践订单: '创建',
    编辑高校实践订单: '编辑',
    审批通过: '审批通过',
    确认收款: 'payment_confirm'
  }
  return textMap[action] || action
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 处理抽屉显示状态更新
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}
</script>

<style scoped lang="scss">
.optlog-container {
  padding: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.log-item {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.log-user {
  font-weight: bold;
  color: #303133;
  font-size: 14px;
}

.log-button {
  font-size: 12px;
  padding: 4px 8px;
}

.log-content {
  .log-action {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }
}

.log-changes {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;

  .change-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .change-field {
      font-weight: bold;
      color: #303133;
      margin-right: 8px;
      min-width: 80px;
    }

    .change-old {
      color: #f56c6c;
      margin-right: 4px;
    }

    .change-arrow {
      color: #909399;
      margin: 0 4px;
    }

    .change-new {
      color: #67c23a;
    }
  }
}

.log-note {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  color: #856404;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-timeline-item__node) {
  background-color: #409eff;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-timeline-item__content) {
  margin-left: 16px;
}
</style>
