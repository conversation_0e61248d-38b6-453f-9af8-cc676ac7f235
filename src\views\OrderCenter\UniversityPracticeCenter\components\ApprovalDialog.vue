<!--
  页面名称：发起审批弹窗
  功能描述：发起审批，支持选择审批结果、输入审批意见
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="发起审批"
    width="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="approval-form">
      <!-- 审批结果 -->
      <el-form-item label="审批结果" prop="result">
        <el-radio-group v-model="form.result" class="approval-result-group">
          <el-radio value="同意" class="approval-radio">
            <div class="radio-content">
              <div class="radio-icon agree">
                <el-icon><Check /></el-icon>
              </div>
              <span>同意</span>
            </div>
          </el-radio>
          <el-radio value="拒绝" class="approval-radio">
            <div class="radio-content">
              <div class="radio-icon reject">
                <el-icon><Close /></el-icon>
              </div>
              <span>拒绝</span>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 审批意见 -->
      <el-form-item label="审批意见" prop="comments">
        <el-input
          v-model="form.comments"
          type="textarea"
          :rows="4"
          placeholder="请输入发起审批的说明 (可选)"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 项目信息 -->
      <el-form-item label="项目信息">
        <div class="project-info">
          <div class="info-item">
            <span class="info-label">项目名称：</span>
            <span class="info-value">{{ orderData?.projectName || '2024年暑期社会实践项目' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">合作高校：</span>
            <span class="info-value">{{ orderData?.university || 'XX大学经济管理学院' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">合作企业：</span>
            <span class="info-value">{{ orderData?.enterprise || 'ABC科技有限公司' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">订单金额：</span>
            <span class="info-value amount">¥{{ orderData?.orderAmount || '580,000' }}</span>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 发起审批 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  result: '同意',
  comments: ''
})

// 表单校验规则
const rules: FormRules = {
  result: [{ required: true, message: '请选择审批结果', trigger: 'change' }]
}

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听弹窗显示状态，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
    }
  }
)

// 重置表单
const resetForm = () => {
  form.value = {
    result: '同意',
    comments: ''
  }
  formRef.value?.clearValidate()
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟接口调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success('审批发起成功')
    emit('success', {
      result: form.value.result,
      comments: form.value.comments,
      operator: '当前用户(管理员)' // 这里应该从用户信息中获取
    })
    handleClose()
  } catch (error) {
    console.error('发起审批失败:', error)
    ElMessage.error('发起审批失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.approval-form {
  padding: 20px 0;
}

.approval-result-group {
  display: flex;
  gap: 20px;
}

.approval-radio {
  .radio-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: 2px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s;

    .radio-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;

      &.agree {
        background: #67c23a;
      }

      &.reject {
        background: #f56c6c;
      }
    }

    span {
      font-weight: 500;
      color: #303133;
    }
  }

  &.is-checked .radio-content {
    border-color: #409eff;
    background: #f0f9ff;
  }
}

.project-info {
  background: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 6px;
  padding: 16px;

  .info-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      flex-shrink: 0;
    }

    .info-value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-radio__input) {
  display: none;
}

:deep(.el-radio__label) {
  padding-left: 0;
}
</style>
