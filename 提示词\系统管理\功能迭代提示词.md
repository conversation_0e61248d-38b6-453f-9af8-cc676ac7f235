# 一.用户管理功能调整

1.调整'src\views\system\user\UserForm.vue'页面：

- 用户昵称前面新增一行，放置账户类型字段(1-内部员工，2-企业用户)，必填项，默认选中企业用户
- 如果选中了企业用户，则在归属部门位置显示归属合作伙伴选项，隐藏归属部门选项
- 如果选中了内部员工，则显示归属部门选项，隐藏归属合作伙伴选项
- 归属合作伙伴下拉框数据来源，请参考'提示词\资源中心提示词\合作伙伴接口文档.md'中获取有效状态合作伙伴列表（下拉框数据）接口文档进行对接
- 表结构请参照'提示词\系统管理\系统管理数据表脚本.md'中的用户表

  2.调整'src\views\system\user\index.vue'页面：

- 列表增加账户类型列accountTypeName，1-内部员工，2-企业用户
- 列表部门列改为归属列，如果账户类型accountType是企业用户，则显示归属合作伙伴名称partnerName，否则显示部门名称deptName
