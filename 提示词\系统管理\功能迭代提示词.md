# 一.用户管理功能调整

## 1.调整'src\views\system\user\UserForm.vue'页面：

- 用户昵称前面新增一行，放置账户类型字段(1-内部员工，2-企业用户)，必填项，默认选中企业用户
- 如果选中了企业用户，则在归属部门位置显示归属合作伙伴选项，隐藏归属部门选项
- 如果选中了内部员工，则显示归属部门选项，隐藏归属合作伙伴选项
- 归属合作伙伴下拉框数据来源，请参考'提示词\资源中心提示词\合作伙伴接口文档.md'中获取有效状态合作伙伴列表（下拉框数据）接口文档进行对接
- 表结构请参照'提示词\系统管理\系统管理数据表脚本.md'中的用户表

  2.调整'src\views\system\user\index.vue'页面：

  - 列表增加账户类型列accountTypeName，1-内部员工，2-企业用户
  - 列表部门列改为归属列，如果账户类型accountType是企业用户，则显示归属合作伙伴名称partnerName，否则显示部门名称deptName

    3.调整左侧部门树：
调整用户管理页面左侧的部门树组件 (`src\views\system\user\index.vue` 中的 DeptTree 组件)，实现部门和合作伙伴的统一树形展示：

**1. 树形结构调整：**
- 在部门树的根级别新增一个虚拟节点："合作伙伴机构"
  - 节点属性：`{ id: 0, name: '合作伙伴机构', type: 2 }`
- 在"合作伙伴机构"节点下，将所有有效的合作伙伴作为子节点
  - 子节点属性：`{ id: 合作伙伴ID, name: 合作伙伴名称, type: 2, parentId: 0 }`
- 为所有原有的真实部门数据添加 `type: 1` 字段标识
  - 部门节点属性：`{ id: 部门ID, name: 部门名称, type: 1, ... }`

**2. 数据来源：**
- 合作伙伴数据：调用 `/publicbiz/partner/list/active` 接口获取有效状态的合作伙伴列表
- 部门数据：保持现有的部门接口调用

**3. 节点类型标识：**
- `type: 1` = 内部部门机构
- `type: 2` = 合作伙伴机构

**4. 选择逻辑调整：**
- 修改 `handleDeptNodeClick` 函数的处理逻辑：
  - 如果选中节点的 `type === 2`（合作伙伴），则设置查询参数 `partnerId = 节点ID`，并清空 `deptId`
  - 如果选中节点的 `type === 1`（内部部门），则设置查询参数 `deptId = 节点ID`，并清空 `partnerId`
  - 如果选中根节点"合作伙伴机构"（`id === 0`），则清空所有筛选条件

**5. 实现要求：**
- 确保树形结构的展开/收起功能正常
- 保持现有的部门树样式和交互体验
- 在用户列表查询时正确传递 `deptId` 或 `partnerId` 参数
- 需要修改相关的数据加载和处理逻辑以支持混合的树形结构
