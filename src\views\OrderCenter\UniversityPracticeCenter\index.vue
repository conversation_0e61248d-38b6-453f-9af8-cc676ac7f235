<!--
  页面名称：高校实践订单首页
  功能描述：展示高校实践订单列表，支持搜索、分页、新增、编辑、删除、查看操作日志
-->
<template>
  <div class="university-practice-index">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">18</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">0</div>
            <div class="stat-label">待处理订单</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">¥2,135,220</div>
            <div class="stat-label">本月订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">92.5%</div>
            <div class="stat-label">订单完成率</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" @submit.prevent="onSearch">
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.orderStatus"
            placeholder="全部订单状态"
            clearable
            style="width: 180px"
          >
            <el-option label="全部订单状态" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="待审批" value="pending_approval" />
            <el-option label="审批中" value="approving" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="待支付" value="pending_payment" />
            <el-option label="执行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select
            v-model="searchForm.paymentStatus"
            placeholder="全部支付状态"
            clearable
            style="width: 180px"
          >
            <el-option label="全部支付状态" value="" />
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已退款" value="refunded" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索项目名称、高校、企业..."
            style="width: 300px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="action-buttons">
        <el-button @click="onExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button type="success" @click="onAdd">
          <el-icon><Plus /></el-icon>
          + 新建高校实践订单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderNumber" label="订单号" width="150" />
        <el-table-column prop="projectUniversity" label="项目/高校" width="300">
          <template #default="scope">
            <div>{{ scope.row.projectName }}</div>
            <div style="color: #909399; font-size: 12px">{{ scope.row.university }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="enterprise" label="合作企业" width="200" />
        <el-table-column prop="projectPeriod" label="项目周期" width="200" />
        <el-table-column prop="manager" label="负责人" width="100" />
        <el-table-column prop="orderAmount" label="订单金额" width="120">
          <template #default="scope"> ¥{{ scope.row.orderAmount }} </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="onView(scope.row)">查看</el-button>
            <el-button size="small" type="warning" @click="onEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
            <el-button size="small" @click="onOptLog(scope.row)">操作日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <AddUniversityPracticeCenter
      v-model:visible="drawerVisible"
      :is-edit="!!editData"
      :order-data="editData"
      @success="onSuccess"
    />

    <!-- 查看详情抽屉 -->
    <IndividualtrainingOrderView
      v-model:visible="viewDrawerVisible"
      :order-data="currentOrderData"
    />

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-data="currentOrderData" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import AddUniversityPracticeCenter from './components/AddUniversityPracticeCenter.vue'
import IndividualtrainingOrderView from './components/IndividualtrainingOrderView.vue'
import OptLog from './components/OptLog.vue'

// 响应式数据
/** 搜索表单数据 */
const searchForm = ref({
  orderStatus: '',
  paymentStatus: '',
  keyword: ''
})

/** 表格数据 */
const tableData = ref([
  {
    id: 1,
    orderNumber: 'HP202406001',
    projectName: '2024年暑期社会实践项目',
    university: 'XX大学经济管理学院',
    enterprise: 'ABC科技有限公司',
    projectPeriod: '2024.07.01 - 2024.08.30',
    manager: '张三',
    orderAmount: '580,000',
    orderStatus: 'in_progress',
    paymentStatus: 'paid',
    createTime: '2024-06-15'
  },
  {
    id: 2,
    orderNumber: 'HP202403001',
    projectName: '春季实习实践项目',
    university: 'YY大学商学院',
    enterprise: 'XYZ制造集团',
    projectPeriod: '2024.03.01 - 2024.04.30',
    manager: '李四',
    orderAmount: '420,000',
    orderStatus: 'completed',
    paymentStatus: 'paid',
    createTime: '2024-02-15'
  },
  {
    id: 3,
    orderNumber: 'HP202406002',
    projectName: 'AI+数字人产业认知项目',
    university: 'ZZ科技大学',
    enterprise: '创新科技集团',
    projectPeriod: '2024.08.01 - 2024.09.30',
    manager: '王五',
    orderAmount: '350,000',
    orderStatus: 'pending_approval',
    paymentStatus: 'pending',
    createTime: '2024-06-20'
  },
  {
    id: 4,
    orderNumber: 'HP202406003',
    projectName: '新媒体运营短学期实践',
    university: 'AA外国语大学',
    enterprise: '星火传媒',
    projectPeriod: '2024.07.10 - 2024.08.10',
    manager: '赵六',
    orderAmount: '280,000',
    orderStatus: 'rejected',
    paymentStatus: 'cancelled',
    createTime: '2024-06-21'
  }
])

/** 分页信息 */
const pagination = ref({
  page: 1,
  size: 10,
  total: 4
})

/** 加载状态 */
const loading = ref(false)

/** 抽屉显示状态 */
const drawerVisible = ref(false)
const viewDrawerVisible = ref(false)
const optLogVisible = ref(false)

/** 编辑数据 */
const editData = ref(null)

/** 当前订单数据 */
const currentOrderData = ref(null)

// 方法
/** 获取订单状态类型 */
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    pending_approval: 'warning',
    approving: 'warning',
    approved: 'success',
    rejected: 'danger',
    pending_payment: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取订单状态文本 */
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    in_progress: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 获取支付状态类型 */
const getPaymentStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    paid: 'success',
    refunded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/** 搜索 */
const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

/** 重置搜索 */
const onReset = () => {
  searchForm.value = {
    orderStatus: '',
    paymentStatus: '',
    keyword: ''
  }
  onSearch()
}

/** 导出 */
const onExport = () => {
  ElMessage.success('导出功能待实现')
}

/** 获取列表数据 */
const fetchList = async () => {
  loading.value = true
  try {
    // TODO: 调用接口获取数据
    // const res = await getUniversityPracticeList({
    //   ...searchForm.value,
    //   page: pagination.value.page,
    //   size: pagination.value.size
    // })
    // tableData.value = res.data.list
    // pagination.value.total = res.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

/** 新增 */
const onAdd = () => {
  editData.value = null
  drawerVisible.value = true
}

/** 编辑 */
const onEdit = (row: any) => {
  editData.value = { ...row }
  drawerVisible.value = true
}

/** 查看 */
const onView = (row: any) => {
  currentOrderData.value = row
  viewDrawerVisible.value = true
}

/** 删除 */
const onDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 直接从表格中删除
    const index = tableData.value.findIndex((item) => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      pagination.value.total--
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

/** 操作日志 */
const onOptLog = (row: any) => {
  currentOrderData.value = row
  optLogVisible.value = true
}

/** 操作成功回调 */
const onSuccess = () => {
  drawerVisible.value = false
  fetchList()
}

/** 分页大小改变 */
const onSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchList()
}

/** 当前页改变 */
const onCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchList()
}

// 生命周期
onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.university-practice-index {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .statistics-section {
    margin-bottom: 20px;

    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #409eff;
      text-align: center;

      .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .search-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .table-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .pagination-section {
      padding: 20px;
      text-align: right;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>
