<!--
  页面名称：上传纸质合同弹窗
  功能描述：上传纸质合同，支持文件上传、合同信息填写
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传纸质合同"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="paper-contract-form"
    >
      <!-- 合同附件 -->
      <el-form-item label="合同附件" prop="contractFile">
        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            :limit="1"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            class="contract-upload"
          >
            <el-button type="primary" size="small">选择文件</el-button>
            <template #tip>
              <div class="upload-tip"> 支持格式: PDF、Word、JPG、PNG, 文件大小不超过10MB </div>
            </template>
          </el-upload>
        </div>
      </el-form-item>

      <!-- 合同编号 -->
      <el-form-item label="合同编号" prop="contractNumber">
        <el-input v-model="form.contractNumber" placeholder="请输入合同编号" style="width: 100%" />
      </el-form-item>

      <!-- 合同名称 -->
      <el-form-item label="合同名称" prop="contractName">
        <el-input v-model="form.contractName" placeholder="请输入合同名称" style="width: 100%" />
      </el-form-item>

      <!-- 签署日期 -->
      <el-form-item label="签署日期" prop="signDate">
        <el-date-picker
          v-model="form.signDate"
          type="date"
          placeholder="年-月-日"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 合同金额 -->
      <el-form-item label="合同金额" prop="contractAmount">
        <el-input v-model="form.contractAmount" placeholder="请输入合同金额" style="width: 100%">
          <template #prepend>¥</template>
        </el-input>
      </el-form-item>

      <!-- 项目信息 -->
      <el-form-item label="项目信息">
        <div class="project-info">
          <div class="info-item">
            <span class="info-label">项目名称：</span>
            <span class="info-value">{{ orderData?.projectName || '2024年暑期社会实践项目' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">合作高校：</span>
            <span class="info-value">{{ orderData?.university || 'XX大学经济管理学院' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">合作企业：</span>
            <span class="info-value">{{ orderData?.enterprise || 'ABC科技有限公司' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">订单金额：</span>
            <span class="info-value amount">¥{{ orderData?.orderAmount || '580,000' }}</span>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadInstance, UploadFile } from 'element-plus'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()

// 加载状态
const loading = ref(false)

// 文件列表
const fileList = ref<UploadFile[]>([])

// 表单数据
const form = ref({
  contractFile: null as File | null,
  contractNumber: '',
  contractName: '',
  signDate: '',
  contractAmount: ''
})

// 表单校验规则
const rules: FormRules = {
  contractFile: [{ required: true, message: '请选择合同文件', trigger: 'change' }],
  contractNumber: [
    { required: true, message: '请输入合同编号', trigger: 'blur' },
    { min: 2, max: 50, message: '合同编号长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  contractName: [
    { required: true, message: '请输入合同名称', trigger: 'blur' },
    { min: 2, max: 100, message: '合同名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  signDate: [{ required: true, message: '请选择签署日期', trigger: 'change' }],
  contractAmount: [
    { required: true, message: '请输入合同金额', trigger: 'blur' },
    {
      pattern: /^\d+(\.\d{1,2})?$/,
      message: '请输入正确的金额格式',
      trigger: 'blur'
    }
  ]
}

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听弹窗显示状态，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
    }
  }
)

// 重置表单
const resetForm = () => {
  form.value = {
    contractFile: null,
    contractNumber: '',
    contractName: '',
    signDate: '',
    contractAmount: ''
  }
  fileList.value = []
  formRef.value?.clearValidate()
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 文件选择变化
const handleFileChange = (file: UploadFile) => {
  // 检查文件大小（10MB）
  const isLt10M = file.size! / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png'
  ]
  if (!allowedTypes.includes(file.raw?.type || '')) {
    ElMessage.error('只支持 PDF、Word、JPG、PNG 格式的文件!')
    return false
  }

  form.value.contractFile = file.raw || null
  return true
}

// 文件移除
const handleFileRemove = () => {
  form.value.contractFile = null
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 创建 FormData 对象用于文件上传
    const formData = new FormData()
    if (form.value.contractFile) {
      formData.append('contractFile', form.value.contractFile)
    }
    formData.append('contractNumber', form.value.contractNumber)
    formData.append('contractName', form.value.contractName)
    formData.append('signDate', form.value.signDate)
    formData.append('contractAmount', form.value.contractAmount)
    formData.append('orderId', props.orderData?.id || '')

    // 模拟接口调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success('纸质合同上传成功')
    emit('success', {
      contractNumber: form.value.contractNumber,
      contractName: form.value.contractName,
      signDate: form.value.signDate,
      contractAmount: form.value.contractAmount
    })
    handleClose()
  } catch (error) {
    console.error('上传纸质合同失败:', error)
    ElMessage.error('上传纸质合同失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.paper-contract-form {
  padding: 20px 0;
}

.upload-area {
  .contract-upload {
    width: 100%;
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
}

.project-info {
  background: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 6px;
  padding: 16px;

  .info-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      flex-shrink: 0;
    }

    .info-value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-list) {
  margin-top: 8px;
}
</style>
