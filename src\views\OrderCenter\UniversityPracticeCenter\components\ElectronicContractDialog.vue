<!--
  页面名称：发起电子合同弹窗
  功能描述：发起电子合同，支持选择合同模板、输入合同名称、展示签署方信息
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="发起电子合同"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="electronic-contract-form"
    >
      <!-- 合同模板 -->
      <el-form-item label="合同模板" prop="templateId">
        <el-select v-model="form.templateId" placeholder="请选择合同模板" style="width: 100%">
          <el-option
            v-for="template in contractTemplates"
            :key="template.id"
            :label="template.name"
            :value="template.id"
          />
        </el-select>
      </el-form-item>

      <!-- 合同名称 -->
      <el-form-item label="合同名称" prop="contractName">
        <el-input v-model="form.contractName" placeholder="请输入合同名称" style="width: 100%" />
      </el-form-item>

      <!-- 签署方信息 -->
      <el-form-item label="签署方信息">
        <div class="signatory-info">
          <div class="signatory-item">
            <span class="party-label">平台方：</span>
            <span class="party-name">川能投教育科技有限公司</span>
          </div>
          <div class="signatory-item">
            <span class="party-label">高校方：</span>
            <span class="party-name">{{ orderData?.university || 'XX大学经济管理学院' }}</span>
          </div>
          <div class="signatory-item">
            <span class="party-label">企业方：</span>
            <span class="party-name">{{ orderData?.enterprise || 'ABC科技有限公司' }}</span>
          </div>
        </div>
      </el-form-item>

      <!-- 项目信息 -->
      <el-form-item label="项目信息">
        <div class="project-info">
          <div class="info-item">
            <span class="info-label">项目名称：</span>
            <span class="info-value">{{ orderData?.projectName || '2024年暑期社会实践项目' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">项目周期：</span>
            <span class="info-value">{{
              orderData?.projectPeriod || '2024.07.01 - 2024.08.30'
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">订单金额：</span>
            <span class="info-value amount">¥{{ orderData?.orderAmount || '580,000' }}</span>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data: any]
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  templateId: '',
  contractName: ''
})

// 合同模板列表
const contractTemplates = ref([
  { id: '1', name: '高校实践项目合同模板' },
  { id: '2', name: '校企合作协议模板' },
  { id: '3', name: '实习实训合同模板' }
])

// 表单校验规则
const rules: FormRules = {
  templateId: [{ required: true, message: '请选择合同模板', trigger: 'change' }],
  contractName: [
    { required: true, message: '请输入合同名称', trigger: 'blur' },
    { min: 2, max: 100, message: '合同名称长度在 2 到 100 个字符', trigger: 'blur' }
  ]
}

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听弹窗显示状态，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
    }
  }
)

// 重置表单
const resetForm = () => {
  form.value = {
    templateId: '',
    contractName: ''
  }
  formRef.value?.clearValidate()
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟接口调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success('电子合同发起成功')
    emit('success', {
      templateId: form.value.templateId,
      contractName: form.value.contractName
    })
    handleClose()
  } catch (error) {
    console.error('发起电子合同失败:', error)
    ElMessage.error('发起电子合同失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.electronic-contract-form {
  padding: 20px 0;
}

.signatory-info {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;

  .signatory-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .party-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      flex-shrink: 0;
    }

    .party-name {
      color: #303133;
      flex: 1;
    }
  }
}

.project-info {
  background: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 6px;
  padding: 16px;

  .info-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      flex-shrink: 0;
    }

    .info-value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
