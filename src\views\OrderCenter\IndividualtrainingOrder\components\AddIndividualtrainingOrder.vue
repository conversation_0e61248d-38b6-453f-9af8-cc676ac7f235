<!--
  页面名称：个人培训订单新增/编辑表单
  功能描述：新增/编辑个人培训订单，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑个人培训订单' : '新增个人培训订单'"
    size="60%"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="individual-training-form"
    >
      <!-- 基本信息模块 -->
      <div class="form-module">
        <div class="module-title">基本信息</div>
        <div class="module-content">
          <el-form-item label="学员姓名" prop="studentName" required>
            <el-input v-model="form.studentName" placeholder="请输入学员姓名" />
          </el-form-item>

          <el-form-item label="联系电话" prop="contactPhone" required>
            <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
          </el-form-item>

          <el-form-item label="身份证号" prop="idCard" required>
            <el-input v-model="form.idCard" placeholder="请输入身份证号" />
          </el-form-item>

          <el-form-item label="邮箱地址" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱地址" />
          </el-form-item>

          <el-form-item label="订单类型" prop="orderType" required>
            <el-select v-model="form.orderType" placeholder="请选择订单类型" style="width: 100%">
              <el-option label="个人培训" value="training" />
              <el-option label="考试认证" value="certification" />
            </el-select>
          </el-form-item>

          <el-form-item label="课程/考试项目" prop="courseName" required>
            <el-select
              v-model="form.courseName"
              placeholder="请选择课程/考试项目"
              style="width: 100%"
            >
              <el-option label="项目管理PMP认证课程" value="pmp_course" />
              <el-option label="Python编程基础课程" value="python_course" />
              <el-option label="高级母婴护理师认证" value="maternal_care" />
              <el-option label="营养师资格认证" value="nutritionist" />
              <el-option label="心理咨询师三级认证" value="psychologist" />
              <el-option label="数据分析师认证课程" value="data_analyst" />
            </el-select>
          </el-form-item>

          <el-form-item label="订单来源" prop="orderSource" required>
            <el-select v-model="form.orderSource" placeholder="请选择订单来源" style="width: 100%">
              <el-option label="线上小程序" value="online_miniprogram" />
              <el-option label="线下报名" value="offline_registration" />
              <el-option label="电话咨询" value="phone_consultation" />
              <el-option label="朋友推荐" value="friend_recommendation" />
            </el-select>
          </el-form-item>

          <el-form-item label="订单金额" prop="orderAmount" required>
            <el-input v-model="form.orderAmount" placeholder="请输入订单金额" type="number">
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>

          <el-form-item label="支付状态" prop="paymentStatus" required>
            <el-select
              v-model="form.paymentStatus"
              placeholder="请选择支付状态"
              style="width: 100%"
            >
              <el-option label="待支付" value="pending" />
              <el-option label="已支付" value="paid" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>

          <el-form-item label="学习/考试状态" prop="learningStatus" required>
            <el-select
              v-model="form.learningStatus"
              placeholder="请选择学习/考试状态"
              style="width: 100%"
            >
              <el-option label="未开始" value="not_started" />
              <el-option label="学习中" value="learning" />
              <el-option label="待考试" value="pending_exam" />
              <el-option label="待确认" value="pending_confirmation" />
              <el-option label="已通过" value="passed" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 合同管理模块 -->
      <div class="form-module">
        <div class="module-title">合同管理</div>
        <div class="module-content">
          <el-form-item label="合同类型" prop="contractType">
            <el-radio-group v-model="form.contractType">
              <el-radio label="electronic">电子合同</el-radio>
              <el-radio label="paper">纸质合同</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="合同模板" prop="contractTemplate">
            <el-select
              v-model="form.contractTemplate"
              placeholder="请选择合同模板"
              style="width: 100%"
            >
              <el-option label="个人培训协议模板" value="training_contract" />
              <el-option label="考试认证协议模板" value="certification_contract" />
              <el-option label="通用协议模板" value="general_contract" />
            </el-select>
          </el-form-item>

          <el-form-item label="合同附件" prop="contractAttachment">
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  {{ fileList.length > 0 ? '已选择文件' : '未选择任何文件' }}
                </div>
              </template>
            </el-upload>
          </el-form-item>

          <el-form-item label="合同编号" prop="contractNumber">
            <el-input v-model="form.contractNumber" placeholder="请输入合同编号" />
          </el-form-item>
        </div>
      </div>

      <!-- 备注信息模块 -->
      <div class="form-module">
        <div class="module-title">备注信息</div>
        <div class="module-content">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </div>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '提交' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, UploadFile } from 'element-plus'

// Props
interface Props {
  visible: boolean
  editData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  editData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
/** 表单引用 */
const formRef = ref<FormInstance>()

/** 表单数据 */
const form = ref({
  studentName: '',
  contactPhone: '',
  idCard: '',
  email: '',
  orderType: '',
  courseName: '',
  orderSource: '',
  orderAmount: '',
  paymentStatus: '',
  learningStatus: '',
  contractType: 'electronic',
  contractTemplate: '',
  contractAttachment: '',
  contractNumber: '',
  remark: ''
})

/** 表单校验规则 */
const rules = {
  studentName: [
    { required: true, message: '请输入学员姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: '请输入正确的身份证号',
      trigger: 'blur'
    }
  ],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
  orderType: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  courseName: [{ required: true, message: '请选择课程/考试项目', trigger: 'change' }],
  orderSource: [{ required: true, message: '请选择订单来源', trigger: 'change' }],
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ],
  paymentStatus: [{ required: true, message: '请选择支付状态', trigger: 'change' }],
  learningStatus: [{ required: true, message: '请选择学习/考试状态', trigger: 'change' }]
}

/** 文件列表 */
const fileList = ref<UploadFile[]>([])

/** 加载状态 */
const loading = ref(false)

/** 是否为编辑模式 */
const isEdit = ref(false)

// 方法
/** 处理文件变化 */
const handleFileChange = (file: UploadFile) => {
  fileList.value = [file]
  form.value.contractAttachment = file.name || ''
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // TODO: 调用接口提交数据
    // 模拟接口调用延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    ElMessage.success(isEdit.value ? '更新成功' : '提交成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

/** 重置表单 */
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  fileList.value = []
  initFormData()
}

/** 关闭抽屉 */
const handleClose = () => {
  emit('update:visible', false)
  handleReset()
}

/** 初始化表单数据 */
const initFormData = () => {
  form.value = {
    studentName: '',
    contactPhone: '',
    idCard: '',
    email: '',
    orderType: '',
    courseName: '',
    orderSource: '',
    orderAmount: '',
    paymentStatus: '',
    learningStatus: '',
    contractType: 'electronic',
    contractTemplate: '',
    contractAttachment: '',
    contractNumber: '',
    remark: ''
  }
}

/** 编辑时回显数据 */
const setEditData = (data: any) => {
  if (data) {
    form.value = { ...data }
    isEdit.value = true
  } else {
    initFormData()
    isEdit.value = false
  }
}

// 监听器
watch(
  () => props.editData,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        setEditData(newVal)
      })
    }
  },
  { immediate: true }
)

watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      handleReset()
    }
  }
)
</script>

<style scoped lang="scss">
.individual-training-form {
  padding: 20px;

  .form-module {
    margin-bottom: 30px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;

    .module-title {
      background: #f5f7fa;
      padding: 12px 20px;
      font-weight: bold;
      color: #303133;
      border-bottom: 1px solid #e4e7ed;
    }

    .module-content {
      padding: 20px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;

      .el-form-item {
        margin-bottom: 0;
      }

      // 某些字段占满整行
      .el-form-item:has(.el-textarea),
      .el-form-item:has(.el-upload) {
        grid-column: 1 / -1;
      }
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e4e7ed;
  }
}

.upload-demo {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
  }
}
</style>
