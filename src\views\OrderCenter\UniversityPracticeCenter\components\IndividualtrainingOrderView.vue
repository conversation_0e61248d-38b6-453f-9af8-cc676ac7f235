<!--
  页面名称：高校实践订单查看详情
  功能描述：展示高校实践订单详情，支持查看合同信息、审批流程、发起电子合同、上传纸质合同、发起审批等功能
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="高校实践订单详情"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="order-detail-container">
      <!-- 项目详情区域 -->
      <div class="detail-section">
        <div class="project-header">
          <div class="project-title">{{ orderData?.projectName || '2024年暑期社会实践项目' }}</div>
          <el-tag type="primary">执行中</el-tag>
        </div>

        <div class="detail-list">
          <div class="detail-item">
            <span class="label">合作高校：</span>
            <span class="value">{{ orderData?.university || 'XX大学经济管理学院' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合作企业：</span>
            <span class="value">{{ orderData?.enterprise || 'ABC科技有限公司' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">项目周期：</span>
            <span class="value">{{ orderData?.projectPeriod || '2024.07.01 - 2024.08.30' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">订单金额：</span>
            <span class="value amount">¥{{ orderData?.orderAmount || '580,000' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">项目负责人：</span>
            <span class="value">{{ orderData?.manager || '张三' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">关联商机：</span>
            <span
              class="value link"
              style="color: #409eff; cursor: pointer; text-decoration: underline"
              >OPP202406001 - XX大学暑期实践项目商机</span
            >
          </div>
          <div class="detail-item full-width">
            <span class="label">项目描述：</span>
            <span class="value"
              >为XX大学经济管理学院学生提供暑期社会实践机会,涉及数字化转型、数据分析等领域。</span
            >
          </div>
        </div>
      </div>

      <!-- 合同信息区域 -->
      <div class="detail-section">
        <div class="section-title">
          <el-icon><Edit /></el-icon>
          合同信息
        </div>

        <div class="detail-list">
          <div class="detail-item">
            <span class="label">合同类型：</span>
            <span class="value">{{ contractInfo.type || '纸质合同' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同编号：</span>
            <span class="value">{{ contractInfo.number || 'HT-202406001' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同名称：</span>
            <span class="value">{{ contractInfo.name || '家政服务合同' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同周期：</span>
            <span class="value">{{ contractInfo.period || '2024-6-1 - 2024-6-30' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">签署日期：</span>
            <span class="value">{{ contractInfo.signDate || '2024-6-1' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同金额：</span>
            <span class="value amount">¥{{ contractInfo.amount || '580,000' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">合同附件：</span>
            <div class="file-item">
              <el-icon><Document /></el-icon>
              <span class="file-name">合同_XX大学暑期实践项目.pdf</span>
              <el-button size="small" type="info" @click="handleDownload">下载</el-button>
            </div>
          </div>
          <div class="detail-item">
            <span class="label">合同状态：</span>
            <span class="value">已生效</span>
          </div>
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">2024-06-01 10:30:00</span>
          </div>
        </div>

        <!-- 合同操作按钮 -->
        <div class="contract-actions">
          <el-button size="small" type="primary" @click="showElectronicContractDialog">
            <el-icon><Document /></el-icon>
            发起电子合同
          </el-button>
          <el-button size="small" type="info" @click="showPaperContractDialog">
            <el-icon><Upload /></el-icon>
            上传纸质合同
          </el-button>
        </div>
      </div>

      <!-- 三方协议签署状态 -->
      <div class="detail-section">
        <div class="section-title">三方协议签署状态</div>

        <div class="sign-status-list">
          <div class="sign-status-item">
            <el-icon class="status-icon"><Platform /></el-icon>
            <span class="party-name">平台方</span>
            <el-tag type="success" size="small">已签署</el-tag>
          </div>
          <div class="sign-status-item">
            <el-icon class="status-icon"><School /></el-icon>
            <span class="party-name">高校方</span>
            <el-tag type="success" size="small">已签署</el-tag>
          </div>
          <div class="sign-status-item">
            <el-icon class="status-icon"><OfficeBuilding /></el-icon>
            <span class="party-name">企业方</span>
            <el-tag type="warning" size="small">待签署</el-tag>
          </div>
        </div>
      </div>

      <!-- 纸质合同附件 -->
      <div class="detail-section">
        <div class="section-title">纸质合同附件</div>

        <div class="contract-attachment">
          <div class="file-item">
            <el-icon><Document /></el-icon>
            <span class="file-name">合同_XX大学暑期实践项目.pdf</span>
            <el-button size="small" type="info" @click="handleDownload">下载</el-button>
          </div>
        </div>
      </div>

      <!-- 收款信息 -->
      <div class="detail-section">
        <div class="section-title">
          <el-icon><Money /></el-icon>
          收款信息
        </div>

        <div class="receipt-info">
          <div class="receipt-item">
            <span class="receipt-label">收款金额：</span>
            <span class="receipt-value amount">¥580,000</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款方式：</span>
            <span class="receipt-value">bank_transfer</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款日期：</span>
            <span class="receipt-value">2024-06-20</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">操作人：</span>
            <span class="receipt-value">张三(管理员)</span>
          </div>
          <div class="receipt-item">
            <span class="receipt-label">收款备注：</span>
            <span class="receipt-value">银行转账</span>
          </div>
        </div>
      </div>

      <!-- 审批流程 -->
      <div class="detail-section">
        <div class="section-title">审批流程</div>

        <div class="approval-timeline">
          <div class="timeline-item" v-for="(item, index) in approvalList" :key="index">
            <div class="timeline-dot"></div>
            <div class="timeline-content">
              <div class="timeline-time">{{ item.time }}</div>
              <div class="timeline-action">
                <strong>{{ item.operator }}</strong> {{ item.action }}
              </div>
              <div class="timeline-note" v-if="item.note">备注: {{ item.note }}</div>
            </div>
          </div>
          <div v-if="approvalList.length === 0" class="no-approval"> 无审批记录 </div>
        </div>

        <!-- 发起审批按钮 -->
        <div class="approval-actions" v-if="orderData?.orderStatus === 'pending_approval'">
          <el-button size="small" type="primary" @click="showApprovalDialog"> 发起审批 </el-button>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 发起电子合同弹窗 -->
  <ElectronicContractDialog
    v-model:visible="electronicContractVisible"
    :order-data="orderData"
    @success="handleElectronicContractSuccess"
  />

  <!-- 上传纸质合同弹窗 -->
  <PaperContractDialog
    v-model:visible="paperContractVisible"
    :order-data="orderData"
    @success="handlePaperContractSuccess"
  />

  <!-- 发起审批弹窗 -->
  <ApprovalDialog
    v-model:visible="approvalVisible"
    :order-data="orderData"
    @success="handleApprovalSuccess"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Document,
  Upload,
  Platform,
  School,
  OfficeBuilding,
  Money,
  Opportunity,
  Edit
} from '@element-plus/icons-vue'
import ElectronicContractDialog from './ElectronicContractDialog.vue'
import PaperContractDialog from './PaperContractDialog.vue'
import ApprovalDialog from './ApprovalDialog.vue'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [data: any]
}>()

// 弹窗显示状态
const electronicContractVisible = ref(false)
const paperContractVisible = ref(false)
const approvalVisible = ref(false)

// 合同信息
const contractInfo = ref({
  type: '纸质合同',
  number: 'HT-202406001',
  name: '家政服务合同',
  period: '2024-6-1 - 2024-6-30',
  signDate: '2024-6-1',
  amount: '580,000'
})

// 审批流程列表
const approvalList = ref([
  {
    time: '2024/6/20 10:05:12',
    operator: '张三(管理员)',
    action: '批准了订单。',
    note: '已确认合作意向,批准立项。'
  }
])

// 根据订单状态初始化审批列表
const initApprovalList = () => {
  if (props.orderData?.orderStatus === 'pending_approval') {
    // 待审批状态，清空审批记录
    approvalList.value = []
  } else if (props.orderData?.orderStatus === 'in_progress') {
    // 执行中状态，显示已审批记录
    approvalList.value = [
      {
        time: '2024/6/20 10:05:12',
        operator: '张三(管理员)',
        action: '批准了订单。',
        note: '已确认合作意向,批准立项。'
      }
    ]
  }
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 编辑
const handleEdit = () => {
  emit('edit', props.orderData)
  handleClose()
}

// 下载合同附件
const handleDownload = () => {
  // TODO: 实现文件下载功能
  console.log('下载合同附件')
}

// 显示发起电子合同弹窗
const showElectronicContractDialog = () => {
  electronicContractVisible.value = true
}

// 显示上传纸质合同弹窗
const showPaperContractDialog = () => {
  paperContractVisible.value = true
}

// 显示发起审批弹窗
const showApprovalDialog = () => {
  approvalVisible.value = true
}

// 电子合同创建成功回调
const handleElectronicContractSuccess = () => {
  // TODO: 刷新合同信息
  console.log('电子合同创建成功')
}

// 纸质合同上传成功回调
const handlePaperContractSuccess = () => {
  // TODO: 刷新合同信息
  console.log('纸质合同上传成功')
}

// 审批发起成功回调
const handleApprovalSuccess = (approvalData: any) => {
  // 添加新的审批记录
  approvalList.value.unshift({
    time: new Date().toLocaleString(),
    operator: approvalData.operator,
    action: approvalData.result === '同意' ? '批准了订单。' : '拒绝了订单。',
    note: approvalData.comments
  })
  console.log('审批发起成功')
}

// 监听订单数据变化，初始化审批列表
watch(
  () => props.orderData,
  (newVal) => {
    if (newVal) {
      initApprovalList()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.order-detail-container {
  padding: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.detail-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom: 0;
  }
}

.project-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  .project-title {
    font-size: 18px;
    font-weight: bold;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-list {
  .detail-item {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;

    &.full-width {
      flex-direction: column;
      align-items: flex-start;
    }

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 100px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }

      &.link {
        color: #409eff;
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .file-name {
    flex: 1;
    color: #303133;
  }
}

.business-opportunity {
  .opportunity-link {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .opportunity-desc {
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }
}

.contract-actions-section {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.enterprise-status {
  display: flex;
  align-items: center;
  gap: 12px;

  .status-label {
    font-weight: 500;
    color: #303133;
  }
}

.contract-attachment {
  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    .file-name {
      flex: 1;
      color: #303133;
      font-weight: 500;
    }
  }
}

.contract-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  justify-content: flex-start;
}

.sign-status-list {
  .sign-status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .status-icon {
      font-size: 20px;
      color: #909399;
    }

    .party-name {
      flex: 1;
      font-weight: 500;
      color: #303133;
    }
  }
}

.receipt-info {
  background: #f0f9ff;
  border: 1px solid #67c23a;
  border-radius: 8px;
  padding: 16px;

  .receipt-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .receipt-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      flex-shrink: 0;
    }

    .receipt-value {
      color: #303133;
      flex: 1;

      &.amount {
        color: #67c23a;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}

.approval-timeline {
  .timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;

    .timeline-dot {
      width: 8px;
      height: 8px;
      background: #409eff;
      border-radius: 50%;
      margin-top: 6px;
      flex-shrink: 0;
    }

    .timeline-content {
      flex: 1;

      .timeline-time {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .timeline-action {
        font-size: 14px;
        color: #303133;
        margin-bottom: 4px;
      }

      .timeline-note {
        font-size: 12px;
        color: #606266;
        background: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
      }
    }
  }

  .no-approval {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
  }
}

.approval-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-drawer__header) {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

:deep(.el-drawer__body) {
  padding: 0;
}
</style>
