<!--
  页面名称：个人培训与认证订单操作日志
  功能描述：查看个人培训与认证订单的操作日志，支持时间线展示
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="个人培训订单 - 操作日志"
    direction="rtl"
    size="600px"
    :before-close="handleClose"
    :append-to-body="true"
    :modal="true"
    :z-index="9999"
  >
    <div class="drawer-content">
      <div class="log-container">
        <!-- 系统自动操作日志 -->
        <div class="log-entry">
          <div class="log-header">
            <span class="log-actor">系统自动</span>
            <span class="log-time">2024/6/10 09:15:00</span>
            <el-button type="success" size="small" text>创建</el-button>
          </div>
          <div class="log-action-box">
            <div class="action-content">学员通过小程序报名</div>
          </div>
        </div>

        <!-- 客服操作日志 -->
        <div class="log-entry">
          <div class="log-header">
            <span class="log-actor">李美丽(客服)</span>
            <span class="log-time">2024/6/10 14:30:00</span>
            <span class="log-action-tag">payment_confirm</span>
          </div>

          <!-- 状态变更信息 -->
          <div class="status-changes">
            <div class="status-item">
              <span class="status-label">支付状态:</span>
              <span class="status-old">待支付</span>
              <span class="status-arrow">→</span>
              <span class="status-new">已支付</span>
            </div>
            <div class="status-item">
              <span class="status-label">订单状态:</span>
              <span class="status-old">待支付</span>
              <span class="status-arrow">→</span>
              <span class="status-new">执行中</span>
            </div>
          </div>

          <div class="log-action-box">
            <div class="action-content">确认支付完成</div>
          </div>
        </div>

        <!-- 更多操作日志示例 -->
        <div class="log-entry">
          <div class="log-header">
            <span class="log-actor">张经理(审核)</span>
            <span class="log-time">2024/6/11 10:15:00</span>
            <el-button type="primary" size="small" text>审核</el-button>
          </div>
          <div class="log-action-box">
            <div class="action-content">审核通过，开始安排课程</div>
          </div>
        </div>

        <div class="log-entry">
          <div class="log-header">
            <span class="log-actor">王老师(教学)</span>
            <span class="log-time">2024/6/12 09:00:00</span>
            <el-button type="warning" size="small" text>教学</el-button>
          </div>
          <div class="log-action-box">
            <div class="action-content">开始第一节课：项目管理基础</div>
          </div>
        </div>

        <div class="log-entry">
          <div class="log-header">
            <span class="log-actor">系统自动</span>
            <span class="log-time">2024/6/15 16:30:00</span>
            <el-button type="info" size="small" text>通知</el-button>
          </div>
          <div class="log-action-box">
            <div class="action-content">发送课程进度提醒邮件</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props
interface Props {
  visible: boolean
  orderId?: string
}

const props = withDefaults(defineProps<Props>(), {
  orderId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
/** 处理关闭 */
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;

  .log-container {
    .log-entry {
      margin-bottom: 24px;
      padding: 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:last-child {
        margin-bottom: 0;
      }

      .log-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;

        .log-actor {
          font-weight: 600;
          color: #333;
          font-size: 14px;
        }

        .log-time {
          color: #666;
          font-size: 12px;
          flex: 1;
        }

        .log-action-tag {
          background: #f0f9ff;
          color: #409eff;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          border: 1px solid #d1ecf1;
        }
      }

      .status-changes {
        margin-bottom: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;

        .status-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 13px;

          &:last-child {
            margin-bottom: 0;
          }

          .status-label {
            color: #666;
            min-width: 70px;
            margin-right: 8px;
          }

          .status-old {
            color: #f56c6c;
            font-weight: 500;
          }

          .status-arrow {
            color: #909399;
            margin: 0 8px;
          }

          .status-new {
            color: #67c23a;
            font-weight: 500;
          }
        }
      }

      .log-action-box {
        background: #fff7e6;
        border-left: 4px solid #ffa500;
        padding: 12px;
        border-radius: 4px;

        .action-content {
          color: #333;
          font-size: 14px;
          line-height: 1.4;
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 20px 0;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-drawer__body) {
  padding: 0;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

:deep(.el-drawer) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  height: 100vh !important;
}

:deep(.el-overlay) {
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998 !important;
}

:deep(.el-button--text) {
  padding: 0;
  height: auto;
  font-size: 12px;
}
</style>
